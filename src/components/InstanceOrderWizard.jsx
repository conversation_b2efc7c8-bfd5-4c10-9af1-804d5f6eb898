import React, { useState, useMemo, useEffect, useRef } from 'react';
import {
  Modal,
  Steps,
  Card,
  Button,
  Form,
  Input,
  Select,
  Switch,
  Alert,
} from 'antd';
import {
  Server,
  Package,
  Settings,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  RefreshCw,
  Edit2,
} from 'lucide-react';
import { formatPrice } from '@/utils/format';
import EnvironmentVariablesInput from './EnvironmentVariablesInput';
import { deployService } from '@/services/deployService';
import { appTemplateService, APP_TYPES } from '@/services/appTemplate';
import { toast } from 'sonner';
import ApplicationSelectionDialog from './ApplicationSelectionDialog';
import { generateName } from '@/lib/utils';
import DiskInput from "./DiskInput"

const { Option } = Select;

const InstanceOrderWizard = ({
  visible,
  onCancel,
  onConfirm,
  device,
  application = null,
  title = '订购实例',
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [appSelectionVisible, setAppSelectionVisible] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState(application);
  const [generatingName, setGeneratingName] = useState(false);

  // 用于管理 debounced 更新的 ref
  const diskSizeTimeoutRef = useRef(null);

  useEffect(() => {
    setSelectedApplication(application);
  }, [application])

  // 订购配置状态
  const [orderConfig, setOrderConfig] = useState({
    duration: 1, // 时长（小时）
    resourceCount: 1, // GPU 卡数或 CPU 核数
    cpuCores: 0, // 配套的CPU核数（自动计算）
    memory: 0, // 配套的内存（自动计算）
    extraDiskSize: 0, // 额外磁盘大小（GB，步进8G）
    autoInstallApp: true,
    autoRenew: false,
    appName: '', // 应用实例名称
  });

  // 强制三个步骤
  const steps = [
    {
      title: '应用选择',
      description: '选择要部署的应用',
      icon: <Package className="w-4 h-4" />,
    },
    {
      title: '硬件配置',
      description: '配置实例硬件资源',
      icon: <Server className="w-4 h-4" />,
    },
    {
      title: '订单确认',
      description: '确认订单并支付',
      icon: <CheckCircle className="w-4 h-4" />,
    },
  ];

  // 计算资源配套
  const calculateResourceAllocation = (device, resourceCount) => {
    if (!device) return { cpuCores: 0, memory: 0 };

    const isGpuDevice = device.gpuNum && device.gpuNum > 0;

    if (isGpuDevice) {
      // GPU设备：按照GPU数量比例计算CPU和内存
      const totalCpuCores = device.cpuNum || 32; // 默认CPU核数
      const totalMemory = parseInt(device.memorySize || device.memory?.match(/\d+/)?.[0] || '16'); // 默认内存GB
      const totalGpus = device.gpuNum;

      // 按GPU比例分配资源
      const cpuCores = Math.round((totalCpuCores / totalGpus) * resourceCount);
      const memory = Math.round((totalMemory / totalGpus) * resourceCount);

      return { cpuCores, memory };
    } else {
      // CPU设备：按照CPU数量比例计算内存
      const totalCpuCores = device.cpuNum || 8;
      const totalMemory = parseInt(device.memorySize || device.memory?.match(/\d+/)?.[0] || '8');

      // 按CPU比例分配内存（通常每个核对应2-4GB内存）
      const memoryPerCore = totalMemory / totalCpuCores;
      const memory = Math.round(memoryPerCore * resourceCount);

      return { cpuCores: resourceCount, memory };
    }
  };

  // 资源变更时更新配套计算
  useEffect(() => {
    if (device) {
      const allocation = calculateResourceAllocation(device, orderConfig.resourceCount);
      setOrderConfig(prev => ({
        ...prev,
        cpuCores: allocation.cpuCores,
        memory: allocation.memory,
      }));
    }
  }, [device, orderConfig.resourceCount]);

  // 同步表单字段值与状态
  useEffect(() => {
    const currentApp = selectedApplication || application;
    const formValues = {
      autoRenew: orderConfig.autoRenew,
      duration: orderConfig.duration,
      extraDiskSize: orderConfig.extraDiskSize,
      appName: orderConfig.appName, // 同步应用实例名称
    };

    // 如果有应用且有环境变量，设置环境变量初始值
    if (currentApp?.environmentVariablesList && currentApp.environmentVariablesList.length > 0) {
      formValues.environmentVariables = currentApp.environmentVariablesList.map(envVar => ({
        key: envVar.key,
        value: envVar.value
      }));
    }
    form.setFieldsValue(formValues);
  }, [
    form,
    orderConfig.autoRenew,
    orderConfig.duration,
    orderConfig.extraDiskSize,
    orderConfig.appName, // 添加 appName 依赖
    selectedApplication,
    application,
  ]);


  // 模拟后端接口生成应用实例名称
  const generateAppInstanceName = async () => {
    setGeneratingName(true);
    try {
      // 模拟API调用，500ms延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 模拟生成的名称（实际使用时替换为真实API调用）
      const generatedName = generateName();

      // 更新orderConfig中的appName
      setOrderConfig(prev => ({
        ...prev,
        appName: generatedName
      }));

      return generatedName;
    } finally {
      setGeneratingName(false);
    }
  };
  // 如果没有传入应用，自动查询基础镜像
  const loadDefaultBaseImage = async () => {
    try {
      const data = await appTemplateService.getAppTemplates({
        appType: APP_TYPES.BASIC_IMAGE,
        status: 1,
        pageIndex: 1,
        pageSize: 1,
      });
      if (data.records && data.records.length > 0) {
        setSelectedApplication(data.records[0]);
      }
    } catch (error) {
      console.error('加载默认基础镜像失败:', error);
    }
  };


  // 合并并简化 useEffect
  useEffect(() => {
    if (!visible) return;

    // 加载默认基础镜像
    if (!application && !selectedApplication) {
      loadDefaultBaseImage();
    }
  }, [visible, device, application]);

  // 清理 timeout
  useEffect(() => {
    return () => {
      if (diskSizeTimeoutRef.current) {
        clearTimeout(diskSizeTimeoutRef.current);
        diskSizeTimeoutRef.current = null;
      }
    };
  }, []);

  // 同步表单字段值与状态
  useEffect(() => {
    const currentApp = selectedApplication || application;
    const formValues = {
      autoRenew: orderConfig.autoRenew,
      duration: orderConfig.duration,
      extraDiskSize: orderConfig.extraDiskSize,
    };

    // 设置环境变量初始值
    if (currentApp?.environmentVariablesList?.length > 0) {
      formValues.environmentVariables = currentApp.environmentVariablesList.map(envVar => ({
        key: envVar.key,
        value: envVar.value
      }));
    }

    form.setFieldsValue(formValues);
  }, [form, orderConfig.autoRenew, orderConfig.duration, orderConfig.extraDiskSize, selectedApplication, application]);

  // 计算价格信息
  const calculatePricing = (device, orderConfig, currentApp) => {
    const appPrice = currentApp?.pricePerHour || 0;

    // 使用详细价格列表 - 参考 ResourceList.jsx 中的方式
    // 优先使用 resourcePricings，然后是 resourcePricingList
    const pricingList = device?.resourcePricings || device?.resourcePricingList || [];

    if (pricingList.length > 0) {
      const gpuPricing = pricingList.find(p => p.resourceType === 'GPU');
      const diskPricing = pricingList.find(p => p.resourceType === 'DISK');

      const gpuUnitPrice = gpuPricing?.pricePerUnit || 0;
      const diskUnitPrice = diskPricing?.pricePerUnit || 0;

      // 判断设备类型
      const isGpuDevice = device.gpuNum && device.gpuNum > 0;
      let resourcePrice = 0;

      if (isGpuDevice) {
        resourcePrice = gpuUnitPrice * orderConfig.resourceCount;
      } else {
        // CPU设备按核数计费（可能需要调整价格计算逻辑）
        resourcePrice = gpuUnitPrice * orderConfig.resourceCount * 0.1; // 假设CPU价格为GPU价格的10%
      }

      const diskHourlyPrice = Math.ceil(orderConfig.extraDiskSize / 8) * diskUnitPrice;

      return {
        gpuUnitPrice,
        diskUnitPrice,
        resourcePrice,
        diskPrice: diskHourlyPrice,
        appPrice,
        appCost: appPrice,
        hourlyTotal: resourcePrice + diskHourlyPrice + appPrice,
      };
    }

    // 使用简单价格数据
    if (device?.pricePerHour) {
      const isGpuDevice = device.gpuNum && device.gpuNum > 0;
      let unitPrice = 0;
      let resourcePrice = 0;

      if (isGpuDevice) {
        unitPrice = device.pricePerHour / (device.gpuNum || 1);
        resourcePrice = unitPrice * orderConfig.resourceCount;
      } else {
        unitPrice = device.pricePerHour / (device.cpuNum || 1);
        resourcePrice = unitPrice * orderConfig.resourceCount;
      }

      const diskUnitPrice = 0.01; // 估算值
      const diskHourlyPrice = Math.ceil(orderConfig.extraDiskSize / 8) * diskUnitPrice;

      return {
        gpuUnitPrice: unitPrice,
        diskUnitPrice,
        resourcePrice,
        diskPrice: diskHourlyPrice,
        appPrice,
        appCost: appPrice,
        hourlyTotal: resourcePrice + diskHourlyPrice + appPrice,
      };
    }

    // 无价格数据
    return {
      gpuUnitPrice: 0,
      diskUnitPrice: 0,
      resourcePrice: 0,
      diskPrice: 0,
      appPrice,
      appCost: appPrice,
      hourlyTotal: appPrice,
    };
  };

  const priceInfo = useMemo(() => {
    const currentApp = selectedApplication || application;
    const pricing = calculatePricing(device, orderConfig, currentApp);
    return {
      ...pricing,
      totalCost: pricing.hourlyTotal * orderConfig.duration,
    };
  }, [device, orderConfig, application, selectedApplication]);

  const handleNext = async () => {
    if (currentStep === 0) {
      // 第一步：应用选择
      try {
        // 检查是否选择了应用
        const currentApp = selectedApplication || application;
        if (!currentApp) {
          toast.error('请选择一个应用');
          return;
        }

        // 验证应用实例名称和环境变量字段
        const fieldsToValidate = ['appName', 'environmentVariables'];
        const values = await form.validateFields(fieldsToValidate);

        // 更新 orderConfig 中的应用实例名称
        setOrderConfig(prev => ({
          ...prev,
          appName: values.appName,
          // 根据应用的最低GPU需求和设备类型设置初始资源数
          resourceCount: Math.max(prev.resourceCount, currentApp.minGpuCount || 1),
        }));

        // 进入下一步
        setCurrentStep(1);
      } catch (error) {
        console.error('应用选择验证失败:', error);
      }
    } else if (currentStep === 1) {
      // 第二步：硬件配置
      try {
        const fieldsToValidate = ['extraDiskSize'];
        const values = await form.validateFields(fieldsToValidate);
        setOrderConfig(prev => ({ ...prev, ...values }));

        setCurrentStep(2);
      } catch (error) {
        console.error('硬件配置验证失败:', error);
      }
    }
  };

  const handlePrev = () => {
    // 总是回到上一步
    setCurrentStep(currentStep - 1);
  };

  const handleConfirm = async () => {
    setLoading(true);
    try {
      const currentApp = selectedApplication || application;

      // 强制要求选择应用
      if (!currentApp) {
        toast.error('请选择一个应用');
        setLoading(false);
        return;
      }

      // 验证第三步的表单字段，包括环境变量
      const fieldsToValidate = ['duration', 'autoRenew', 'environmentVariables'];
      const formValues = await form.validateFields(fieldsToValidate);

      // 更新 orderConfig 中的值
      setOrderConfig(prev => ({ ...prev, ...formValues }));

      // 合并环境变量：预设环境变量 + 用户自定义环境变量
      const envVars = {};

      // 添加应用模板的预设环境变量
      if (currentApp.environmentVariablesList && currentApp.environmentVariablesList.length > 0) {
        currentApp.environmentVariablesList.forEach(envVar => {
          if (envVar.key && envVar.value) {
            envVars[envVar.key] = envVar.value;
          }
        });
      }

      // 添加用户自定义环境变量（会覆盖预设的同名变量）
      if (formValues.environmentVariables) {
        formValues.environmentVariables.forEach(envVar => {
          if (envVar.key && envVar.value) {
            envVars[envVar.key] = envVar.value;
          }
        });
      }

      // 使用自动生成的应用实例名称
      const appInstanceName = orderConfig.appName;

      // 构建部署请求数据
      const deployData = {
        machineId: device.id.toString(),
        appId: parseInt(currentApp.id),
        appName: appInstanceName,
        cpuRequest: orderConfig.cpuCores, // 使用计算出的CPU核数
        memoryRequest: orderConfig.memory, // 使用计算出的内存
        gpuRequest: device.gpuNum && device.gpuNum > 0 ? orderConfig.resourceCount : 0, // GPU设备使用资源数，CPU设备为0
        envVars: envVars,
      };

      // 调用部署接口
      const deploymentId = await deployService.deployApp(deployData);

      toast.success('应用部署请求已提交，正在创建实例...');

      // 构建最终配置，包含部署信息
      const finalConfig = {
        ...orderConfig,
        device,
        application: currentApp,
        totalCost: priceInfo.totalCost,
        pricePerHour:
          priceInfo.resourcePrice + priceInfo.diskPrice + priceInfo.appCost,
        priceInfo,
        deploymentId,
        appName: appInstanceName,
        envVars: envVars,
      };

      await onConfirm(finalConfig);
    } catch (error) {
      setLoading(false);
    }
    // 注意：不在这里设置loading为false，因为需要保持loading状态直到跳转完成
  };

  // 重置组件状态
  const resetState = () => {
    form.resetFields();
    setCurrentStep(0);
    setOrderConfig({
      duration: 1,
      resourceCount: 1,
      cpuCores: 0,
      memory: 0,
      extraDiskSize: 0,
      autoInstallApp: true,
      autoRenew: false,
      appName: '',
    });
    // 清理 timeout
    if (diskSizeTimeoutRef.current) {
      clearTimeout(diskSizeTimeoutRef.current);
      diskSizeTimeoutRef.current = null;
    }
  };

  const handleCancel = () => {
    resetState();
    onCancel();
  };

  // 提取常用UI组件
  const InfoRow = ({ label, value, className = "" }) => (
    <div className={`flex justify-between ${className}`}>
      <span className="text-sm text-gray-600">{label}</span>
      <span className="font-medium">{value}</span>
    </div>
  );



  const SectionCard = ({ title, icon, children, extra = null, size = "small" }) => (
    <Card
      title={
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center space-x-2">
            {icon}
            <span>{title}</span>
          </div>
          {extra}
        </div>
      }
      size={size}
    >
      {children}
    </Card>
  );

  const handleApplicationSelect = async (appId) => {
    try {
      const appDetails = await appTemplateService.getAppTemplateById(appId);
      setSelectedApplication(appDetails);

      setAppSelectionVisible(false);
    } catch (error) {
      console.error('获取应用详情失败:', error);
      toast.error('获取应用详情失败');
    }
  };

  // 第一步：应用选择
  const renderStep1 = () => {
    const currentApp = selectedApplication || application;
    return (
      <div className="space-y-6">
        {!currentApp && (
          <Alert
            message="请选择应用"
            description="您需要选择一个应用来创建实例，应用将自动安装并配置。"
            type="warning"
            showIcon
            action={
              <Button
                type="primary"
                size="small"
                onClick={() => setAppSelectionVisible(true)}
              >
                选择应用
              </Button>
            }
          />
        )}

        <SectionCard
          title="应用信息"
          icon={<Package className="w-4 h-4" />}
          extra={
            <Button
              type="default"
              size="small"
              icon={<Edit2 className="w-3 h-3" />}
              onClick={() => setAppSelectionVisible(true)}
            >
              {currentApp ? '替换应用' : '选择应用'}
            </Button>
          }
        >
          {currentApp ? (
            <div>
              <div className="flex items-center space-x-4 mb-3">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  {currentApp.iconUrl ? (
                    <img
                      src={currentApp.iconUrl}
                      alt={currentApp.name}
                      className="w-8 h-8 rounded"
                    />
                  ) : (
                    <Package className="w-6 h-6 text-blue-600" />
                  )}
                </div>
                <div>
                  <div className="font-medium">{currentApp.name}</div>
                  <div className="text-sm text-gray-600">v{currentApp.version}</div>
                </div>
              </div>
              <div className="text-sm text-gray-700 mb-3">
                {currentApp.description || '没有描述'}
              </div>

              <div className="space-y-3">
                {/* <InfoRow
                  label="应用价格"
                  value={priceInfo.appPrice > 0 ? `${formatPrice(priceInfo.appPrice)}/小时` : '免费'}
                  className="text-green-600"
                /> */}
                {currentApp.minGpuCount > 0 && (
                  <InfoRow
                    label="最低GPU需求"
                    value={`${currentApp.minGpuCount}张`}
                    className="text-orange-600"
                  />
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">请选择一个应用来创建实例</p>
              <Button
                type="primary"
                onClick={() => setAppSelectionVisible(true)}
              >
                选择应用
              </Button>
            </div>
          )}
        </SectionCard>

        {/* 应用实例名称配置 - 独立的卡片，更加突出 */}
        {currentApp && (
          <SectionCard
            title="实例名称配置"
            icon={<Edit2 className="w-4 h-4" />}
          >
            <Form form={form} layout="vertical">
              <Form.Item
                name="appName"
                label="应用实例名称"
                rules={[
                  { required: true, message: '请输入应用实例名称' },
                  { min: 2, message: '实例名称至少2个字符' },
                  { max: 50, message: '实例名称不能超过50个字符' },
                  {
                    pattern: /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/,
                    message: '实例名称只能包含字母、数字、中文、下划线和连字符'
                  }
                ]}
              >
                <Input
                  placeholder="请输入应用实例名称，如：my-app-prod"
                  className="flex-1"
                  suffix={
                    <Button
                      type="link"
                      className='px-0'
                      icon={<RefreshCw className="w-4 h-4" />}
                      loading={generatingName}
                      onClick={async () => {
                        const generatedName = await generateAppInstanceName();
                        form.setFieldValue('appName', generatedName);
                      }}
                      title="自动生成名称"
                    >
                      自动生成
                    </Button>
                  }
                />
              </Form.Item>
            </Form>
          </SectionCard>
        )}

        {currentApp && (
          <Form form={form} layout="vertical">
            <EnvironmentVariablesInput
              title="环境变量配置"
              size="small"
              showTitle={true}
              fieldName="environmentVariables"
              placeholder={{
                key: '如: API_KEY',
                value: '如: your-api-key-here'
              }}
              helpText="💡 提示：环境变量已预填充应用模板的默认配置，您可以自由修改、添加或删除环境变量。"
            />
          </Form>
        )}
      </div>
    );
  };



  // 第二步：硬件配置
  const renderStep2 = () => {
    const currentApp = selectedApplication || application;
    const isGpuDevice = device?.gpuNum && device?.gpuNum > 0;
    const minResourceCount = currentApp?.minGpuCount || 1;
    const maxResourceCount = isGpuDevice ? (device?.gpuNum || 1) : (device?.cpuNum || 8);
    const resourceLabel = isGpuDevice ? 'GPU卡数' : 'CPU核数';
    const resourceUnit = isGpuDevice ? '卡' : '核';

    return (
      <div className="space-y-6">
        <SectionCard
          title="设备信息"
          icon={<Server className="w-4 h-4" />}
        >
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="设备名称" value={device?.machineName || device?.name} />
            <InfoRow label="设备类型" value={device?.machineType || device?.type} />
            <InfoRow label="CPU" value={`${device?.cpuCode || device?.cpu} (${device?.cpuNum}核)`} />
            <InfoRow label="内存" value={`${device?.memorySize || device?.memory}GB`} />
            {isGpuDevice && (
              <InfoRow label="GPU" value={`${device?.gpuName || device?.gpu} (最多${device?.gpuNum}卡)`} />
            )}
            <InfoRow label="存储" value={`${device?.diskSize || device?.storage}GB ${device?.diskType || ''}`} />
          </div>
        </SectionCard>

        <SectionCard
          title="硬件配置"
          icon={<Settings className="w-4 h-4" />}
        >
          <Form
            form={form}
            layout="horizontal"
            labelAlign="left"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
          >
            <Form.Item label={resourceLabel} className="mb-4">
              <div className="space-y-2">
                <Select
                  value={orderConfig.resourceCount}
                  onChange={value =>
                    setOrderConfig(prev => ({ ...prev, resourceCount: value }))
                  }
                  className="w-full"
                >
                  {Array.from({ length: maxResourceCount }, (_, i) => {
                    const resourceCount = i + 1;
                    const isDisabled = resourceCount < minResourceCount;
                    return (
                      <Option key={resourceCount} value={resourceCount} disabled={isDisabled}>
                        {resourceCount}{resourceUnit} {isDisabled && '(低于应用最低需求)'}
                      </Option>
                    );
                  })}
                </Select>
                {minResourceCount > 1 && (
                  <div className="text-sm text-orange-600">
                    ⚠️ 当前应用最低需要 {minResourceCount} {resourceUnit}
                  </div>
                )}
              </div>
            </Form.Item>

            {/* 额外磁盘配置 */}
            <Form.Item label="额外磁盘" className="mb-4">
              <div className="space-y-3">
                <Form.Item
                  name="extraDiskSize"
                  className="mb-0"
                  rules={[
                    {
                      validator: (_, value) => {
                        const num = Number(value);
                        if (isNaN(num) || num < 0 || num % 8 != 0) {
                          return Promise.reject(new Error('磁盘大小必须是 8 的倍数'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <DiskInput />
                </Form.Item>
              </div>
            </Form.Item>
          </Form>
        </SectionCard>

        {/* 资源配置预览 */}
        <SectionCard
          title="资源配套"
          icon={<Settings className="w-4 h-4" />}
        >
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <InfoRow
                label="配套CPU"
                value={`${orderConfig.cpuCores}核`}
                className="text-gray-600"
              />
              <InfoRow
                label="配套内存"
                value={`${orderConfig.memory}GB`}
                className="text-gray-600"
              />
            </div>
          </div>
        </SectionCard>
      </div>
    );
  };

  // 第三步：订单确认
  const renderStep3 = () => {
    const currentApp = selectedApplication || application;
    const appInstanceName = orderConfig.appName;
    const isGpuDevice = device?.gpuNum && device?.gpuNum > 0;
    const resourceLabel = isGpuDevice ? 'GPU卡数' : 'CPU核数';
    const resourceUnit = isGpuDevice ? '卡' : '核';

    return (
      <div className="space-y-6">

        <Card title="配置摘要" size="small">
          <div className="space-y-3">
            {currentApp && (
              <>
                <InfoRow label="实例名称" value={appInstanceName} className="text-blue-600" />
                <InfoRow label="应用" value={`${currentApp.name} v${currentApp.version}`} />
              </>
            )}
            <InfoRow label="设备" value={device?.machineName || device?.name} />
            <InfoRow label={resourceLabel} value={`${orderConfig.resourceCount}${resourceUnit}`} />
            <InfoRow label="配套资源" value={`${orderConfig.cpuCores}核/${orderConfig.memory}GB`} />
            {orderConfig.extraDiskSize > 0 && (
              <InfoRow label="额外磁盘" value={`${orderConfig.extraDiskSize}GB`} />
            )}
          </div>
        </Card>

        <Card title="使用时间与续费设置" size="small">
          <Form
            form={form}
            layout="horizontal"
            labelAlign="left"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
          >
            <Form.Item
              name="duration"
              label="使用时间"
              rules={[
                { required: true, message: '请选择使用时间' },
                { type: 'number', min: 1, message: '使用时间至少为1小时' }
              ]}
            >
              <Select
                value={orderConfig.duration}
                onChange={value => setOrderConfig(prev => ({ ...prev, duration: value }))}
                placeholder="请选择使用时间"
              >
                <Option value={1}>1小时</Option>
                <Option value={2}>2小时</Option>
                <Option value={4}>4小时</Option>
                <Option value={8}>8小时</Option>
                <Option value={12}>12小时</Option>
                <Option value={24}>24小时（1天）</Option>
                <Option value={48}>48小时（2天）</Option>
                <Option value={72}>72小时（3天）</Option>
                <Option value={168}>168小时（1周）</Option>
                <Option value={720}>720小时（1个月）</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="autoRenew"
              label="自动续费"
              valuePropName="checked"
              initialValue={false}
            >
              <Switch
                checkedChildren="开启"
                unCheckedChildren="关闭"
                onChange={checked =>
                  setOrderConfig(prev => ({ ...prev, autoRenew: checked }))
                }
              />
            </Form.Item>
          </Form>
          {orderConfig.autoRenew && (
            <div className="mt-3 p-3 bg-yellow-50 rounded-lg">
              <div className="text-sm text-yellow-800">
                ⚠️ 开启自动续费后，系统将在实例到期前自动续费，请确保账户余额充足。
              </div>
            </div>
          )}
        </Card>



        <Card title="费用明细" size="small">
          <div className="space-y-3">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-gray-700 mb-2">小时费用明细</div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>{isGpuDevice ? 'GPU' : 'CPU'}费用 ({orderConfig.resourceCount}{resourceUnit})</span>
                  <span>
                    {formatPrice(priceInfo.gpuUnitPrice)} × {orderConfig.resourceCount}{resourceUnit} = {formatPrice(priceInfo.resourcePrice)}/小时
                  </span>
                </div>
                {orderConfig.extraDiskSize > 0 && (
                  <div className="flex justify-between">
                    <span>额外磁盘费用 ({orderConfig.extraDiskSize}GB)</span>
                    <span>
                      {formatPrice(priceInfo.diskUnitPrice)} × {Math.ceil(orderConfig.extraDiskSize / 8)}个8GB = {formatPrice(priceInfo.diskPrice)}/小时
                    </span>
                  </div>
                )}
                {currentApp && priceInfo.appCost > 0 && (
                  <div className="flex justify-between">
                    <span>应用费用</span>
                    <span>{formatPrice(priceInfo.appCost)}/小时</span>
                  </div>
                )}
                <div className="border-t pt-2 flex justify-between font-medium">
                  <span>小时单价合计</span>
                  <span>{formatPrice(priceInfo.hourlyTotal)}/小时</span>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-blue-700 mb-2">总费用</div>
              <div className="text-sm text-blue-600">
                {formatPrice(priceInfo.hourlyTotal)}/小时 × {orderConfig.duration}小时 = <span className='text-xl font-semibold'>{formatPrice(priceInfo.totalCost)}</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    );
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={800}
      maskClosable={false}
    >
      <div className="space-y-6">
        {/* 步骤指示器 */}
        <Steps current={currentStep} size="small" className="mb-6">
          {steps.map((step, index) => (
            <Steps.Step
              key={index}
              title={step.title}
              description={step.description}
              icon={<div className="flex items-center justify-center mt-1">{step.icon}</div>}
            />
          ))}
        </Steps>

        {/* 步骤内容 */}
        <div className="min-h-[400px]">
          {currentStep === 0 && renderStep1()}
          {currentStep === 1 && renderStep2()}
          {currentStep === 2 && renderStep3()}
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-between">
          <div>
            {currentStep > 0 && (
              <Button
                onClick={handlePrev}
                icon={<ArrowLeft className="w-4 h-4" />}
              >
                上一步
              </Button>
            )}
          </div>
          <div className="space-x-2">
            <Button onClick={handleCancel}>取消</Button>
            {/* 判断是否是最后一步 */}
            {currentStep === 2 ? (
              <Button type="primary" loading={loading} onClick={handleConfirm}>
                {loading ? '实例创建中...' : '确认订购'}
              </Button>
            ) : (
              <Button
                type="primary"
                onClick={handleNext}
                icon={<ArrowRight className="w-4 h-4" />}
              >
                下一步
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 应用选择对话框 */}
      <ApplicationSelectionDialog
        visible={appSelectionVisible}
        onClose={() => setAppSelectionVisible(false)}
        onSelect={handleApplicationSelect}
      />
    </Modal>
  );
};

export default InstanceOrderWizard;
